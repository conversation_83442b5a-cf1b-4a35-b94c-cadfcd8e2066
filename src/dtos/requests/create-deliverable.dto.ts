import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsBoolean,
  IsOptional,
  IsArray,
  ValidateIf,
} from 'class-validator';
import { OwnerDto } from '../owner.dto';

export class CreateDeliverableRequestDto {
  @ApiProperty({
    description: 'Name of the deliverable',
    example: 'Monthly Revenue Report',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Business function or department this deliverable belongs to',
    example: 'Finance',
  })
  @IsString()
  @IsOptional()
  businessFunction: string;

  @ApiProperty({
    description: 'How often this deliverable is generated or updated',
    example: 'Monthly',
  })
  @IsString()
  @IsOptional()
  frequency: string;

  @ApiProperty({
    description: 'Whether this deliverable is currently active and in use',
    example: true,
  })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({
    description: 'Method or formula used to calculate this deliverable',
    example: 'Sum of all revenue streams minus returns and discounts',
  })
  @IsString()
  calculationMethod: string;

  @ApiProperty({
    description: 'Detailed definition and purpose of this deliverable',
    example:
      'Monthly report showing total revenue generated across all business units',
  })
  @IsString()
  definition: string;

  @ApiProperty({
    description: 'Performance Analytics value or target for this deliverable',
    example: '$10M monthly target',
  })
  @IsString()
  @IsOptional()
  paValue: string;

  @ApiProperty({
    description:
      'Start date for this deliverable. Accepts YYYY format (e.g., "2024") or full date string/Date object',
    oneOf: [
      { type: 'string', example: '2024' },
      {
        type: 'string',
        format: 'date-time',
        example: '2024-01-01T00:00:00.000Z',
      },
    ],
  })
  @IsOptional()
  @ValidateIf((o) => o.dateStart !== undefined)
  @IsString()
  dateStart?: string | Date;

  @ApiPropertyOptional({
    description:
      'End date for this deliverable. Accepts YYYY format (e.g., "2024") or full date string/Date object. If not provided and dateStart is YYYY format, will be auto-set to December 31st of that year',
    oneOf: [
      { type: 'string', example: '2024' },
      {
        type: 'string',
        format: 'date-time',
        example: '2024-12-31T23:59:59.999Z',
      },
    ],
  })
  @IsOptional()
  @ValidateIf((o) => o.dateEnd !== undefined)
  @IsString()
  dateEnd?: string | Date;

  @ApiPropertyOptional({
    description: 'How this deliverable is aggregated at business unit level',
    example: 'Sum across all BUs',
  })
  @IsOptional()
  @IsString()
  buLevelAggregation?: string;

  @ApiPropertyOptional({
    description: 'Code of the deliverable type this deliverable belongs to',
    example: 'KPI',
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description:
      'Array of deliverable UIDs that are associated with this deliverable',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '987fcdeb-51a2-43d1-9f4e-123456789abc',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  deliverableUids?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  scopedDeliverables?: CreateDeliverableRequestDto[];

  @ApiPropertyOptional({
    type: String,
  })
  @IsOptional()
  dataSource?: string;
  @ApiPropertyOptional({
    description:
      'Array of deliverable UIDs that are associated with this deliverable',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '987fcdeb-51a2-43d1-9f4e-123456789abc',
    ],
  })
  @IsOptional()
  @IsArray()
  owners?: OwnerDto[];
}
