import { Injectable } from '@nestjs/common';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';

type TokenData = {
  userId: string;
  sub: string;
  inBehalfOf: string;
  roles?: string[];
  hierarchyLevel: string;
  isManager: boolean;
  globalId: string;
};


@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET
    });
  }

  async validate(payload: any): Promise<TokenData> {
    return {
      userId: payload.uid,
      sub: payload.sub,
      roles: [],
      inBehalfOf: payload.inBehalfOf,
      hierarchyLevel: payload.hierarchyLevel,
      isManager: payload.isManager,
      globalId: payload.globalId
    };
  }
}
