import {
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { AuthGuard } from '@nestjs/passport';
import { IncomingMessage as Request } from 'http';

import { CacheService } from 'src/cache/cache.service';
import { IS_PUBLIC_KEY } from 'src/contants';

declare global {
  type IncomingMessage = Request & { user: string | { [key: string]: any } } & {
    route: { path: string };
  } & { session: { user: any } };
}

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    private readonly cacheService: CacheService,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const httpContext = context.switchToHttp();
    const request: IncomingMessage = httpContext.getRequest();
    const authorization = request.headers.authorization?.replace('Bearer ', '');
    try {
      if (authorization) {
        this.jwtService.verify(authorization, {
          secret: process.env.JWT_SECRET,
        });

        const sessionToken =
          request.headers?.cookie
            ?.split(';')
            ?.find((c) => c.trim().startsWith('northstar.session-token='))
            ?.split('=')[1] ?? request.headers?.['x-session-token'];
        const data: any = await this.cacheService.get(
          `user:session:${sessionToken}`,
        );
        request.session = { user: data?.northstar };

        const canActivateResult = await super.canActivate(context);
        return !!canActivateResult;
      }
      return false;
    } catch (error) {
      const MAPPED_JWT_EXPIRED_RESPONSE = 'jwt expired';

      if (error.message === MAPPED_JWT_EXPIRED_RESPONSE) {
        throw new UnauthorizedException('Token expired');
      }

      return false;
    }
  }

  handleRequest(err, user) {
    if (err || !user) {
      throw err || new UnauthorizedException();
    }
    return user;
  }
}
