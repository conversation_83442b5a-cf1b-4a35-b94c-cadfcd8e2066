import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { AppController } from './app.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSourceOptions } from 'typeorm';
import {
  DeliverableEntity,
  DeliverableOwnerEntity,
  DeliverableRepository,
  DeliverableTypeRepository,
  DeliverableTypeEntity,
  EmployeeEntity,
  EmployeeRepository,
  BusinessFunctionEntity,
  BusinessFunctionRepository,
} from '@ghq-abi/northstar-domain';
import { JwtService } from '@nestjs/jwt';
import { APP_GUARD } from '@nestjs/core';

import { typeorm } from './configs/typeorm.config';
import { AppService } from './app.service';
import { DeliverableOwnerRepository } from '@ghq-abi/northstar-domain';
import { CacheModule } from './cache/cache.module';
import { JwtAuthGuard } from './guards/jwt.guard';
import { JwtStrategy } from './strategies/jwt.strategy';
import { ExtraLogMiddleware } from './middlewares/extra-log.middleware';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [typeorm],
    }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (
        configService: ConfigService,
      ): Promise<DataSourceOptions> =>
        configService.get<DataSourceOptions>('typeorm'),
    }),
    TypeOrmModule.forFeature([
      DeliverableEntity,
      DeliverableOwnerEntity,
      DeliverableTypeEntity,
      EmployeeEntity,
      BusinessFunctionEntity,
    ]),
    CacheModule,
  ],
  controllers: [AppController],
  providers: [
    JwtService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard
    },
    {
      provide: JwtStrategy,
      useFactory: () => new JwtStrategy()
    },
    ExtraLogMiddleware,
    AppService,
    DeliverableRepository,
    DeliverableOwnerRepository,
    DeliverableTypeRepository,
    EmployeeRepository,
    BusinessFunctionRepository,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(ExtraLogMiddleware).forRoutes({
      path: '*',
      method: RequestMethod.ALL
    });
  }
}
