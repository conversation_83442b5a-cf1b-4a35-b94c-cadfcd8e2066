import tracer from 'dd-trace';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import * as compress from 'compression';
import helmet from 'helmet';
import * as cookieParser from 'cookie-parser';

process.env.DD_ENV &&
  tracer.init({
    service: 'tsc-kpi-catalog-api',
    logInjection: true,
    env: process.env.DD_ENV,
    runtimeMetrics: true,
    logLevel: 'debug',
    startupLogs: true,
    profiling: true,
    clientIpEnabled: true,
    reportHostname: true,
    plugins: true,
    tags: {
      'tsc-kpi-catalog-api': 'tsc-kpi-catalog-api',
    },
  });

const API_VERSION = 'v2';
const ROOT_PATH = `api/kpi-catalog/${API_VERSION}`;
const API_DOCS_URL = '/docs';
// const HEALTH_CHECK_URL = '/health-check';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
    }),
  );

  // cors for security
  app.enableCors({
    origin:
      process.env.APP_ENV === 'local'
        ? 'http://localhost:3000'
        : /.*.ab-inbev.com$/,
    credentials: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    allowedHeaders: 'Authorization,Origin,X-Requested-With,Content-Type,Accept,X-Session-Token',
  });
  // cookie parser
  app.use(cookieParser());
  // helmet for security
  app.use(helmet());
  // gzip compression
  app.use(compress());

  const config = new DocumentBuilder()
    .setTitle('KPI Catalog API')
    .setDescription('The KPI Catalog API')
    .setVersion('0.1')
    .addServer(`http://localhost:${process.env.PORT}/`, 'local')
    .addServer(`https://tsc-dev.ab-inbev.com/${ROOT_PATH}`, 'dev')
    .addServer(`https://tsc-qa.ab-inbev.com/${ROOT_PATH}`, 'qa')
    .addServer(`https://northstar.ab-inbev.com/${ROOT_PATH}`, 'prod')
    .build();
  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup(API_DOCS_URL, app, documentFactory);

  await app.listen(process.env.PORT);
}
bootstrap();
