import { Inject, Injectable } from '@nestjs/common';
import { Cache<PERSON>lient, CacheClient<PERSON>ey } from './cache.provider';

@Injectable()
export class CacheService {
  public constructor(
    @Inject(CacheClientKey)
    private readonly client: CacheClient
  ) {}

  async set<T>(key: string, value: T, expirationSeconds?: number) {
    if (expirationSeconds !== undefined) {
      await this.client.set(key, JSON.stringify(value), 'EX', expirationSeconds);
    } else {
      await this.client.set(key, JSON.stringify(value));
    }
  }

  async get<T>(key: string): Promise<T | null> {
    const result = await this.client.get(key);
    if (!result) return null;
    return JSON.parse(result);
  }

  async delete(hash: string): Promise<void> {
    await this.client.del(hash);
  }

  async clearByPrefix(prefix: string): Promise<void> {
    const keys = await this.client.keys(`${prefix}*`);

    if (keys.length) {
      await this.client.del(...keys);
    }
  }
}
