import { Provider } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

export const CacheClientKey = 'CacheClient';
export type CacheClient = Redis;

const configService = new ConfigService();

export const cacheProvider: Provider = {
  useFactory: (): CacheClient => {
    return new Redis({
      host: configService.get('REDIS_HOST'),
      port: configService.get('REDIS_PORT'),
    });
  },
  provide: CacheClientKey,
};
